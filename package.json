{"name": "teledrive", "version": "2.5.5", "repository": "**************:mgilangjanuar/teledrive.git", "author": "<PERSON> <PERSON> <<EMAIL>>", "license": "MIT", "private": true, "engines": {"node": "18.16.0"}, "scripts": {"web": "yarn workspace web", "server": "yarn workspace api", "api": "yarn workspace api", "build": "yarn workspaces run build", "start": "cd api && node dist/index.js", "docs": "USE_SSH=true cd docs && yarn deploy"}, "workspaces": ["web", "api"], "dependencies": {}}