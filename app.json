{"name": "TeleDrive", "description": "Free cloud storage via telegram", "keywords": ["cloud", "drive", "telegram", "free", "storage", "unlimited"], "website": "https://teledriveapp.com", "repository": "https://github.com/mgilangjanuar/teledrive", "addons": ["heroku-postgresql"], "env": {"ENV": {"description": "Hide the logs for production, default: develop. Read more at: https://teledriveapp.com/docs/Deployment/heroku", "required": false}, "REACT_APP_API_URL": {"description": "Base URL for the API, default: '' (empty string).", "required": false}, "REACT_APP_TG_API_HASH": {"description": "Application hash from Telegram App (for experimental features).", "required": true}, "REACT_APP_TG_API_ID": {"description": "Application ID from your Telegram App (for experimental features).", "required": true}, "TG_API_HASH": {"description": "Application hash from Telegram App.", "required": true}, "TG_API_ID": {"description": "Application ID from your Telegram App.", "required": true}, "ADMIN_USERNAME": {"description": "Username Telegram for admin account.", "required": true}, "USE_PSQL_HEROKU": {"description": "If we are using Heroku PSQL", "required": true, "value": "1"}}}