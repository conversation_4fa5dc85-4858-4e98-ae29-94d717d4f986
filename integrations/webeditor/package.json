{"name": "webeditor", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@expo/webpack-config": "~0.16.2", "draft-js": "^0.11.7", "expo": "~45.0.0", "expo-status-bar": "~1.3.0", "react": "17.0.2", "react-dom": "17.0.2", "react-native": "0.68.2", "react-native-web": "0.17.7", "webpack-dev-server": "~3.11.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@types/react": "~17.0.21", "@types/react-native": "~0.66.13", "typescript": "~4.3.5"}, "private": true}