on:
  push:
    branches:
      - main

name: Build Prod

env:
  APP_DIR: /root/teledrive
  DEV_USER: root
  DEV_HOST_SG_1: **************
  DEV_HOST_SG_2: **********
  DEV_HOST_GE_1: **************
  DEV_HOST_GE_2: **********
  DEV_HOST_US_1: ***************

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v1
      with:
        node-version: '14.x'
    - name: Add npmrc
      run: echo -e "//npm.pkg.github.com/:_authToken=${NPM_AUTH_TOKEN}\n@mgilangjanuar:registry=https://npm.pkg.github.com/" > ~/.npmrc && cat ~/.npmrc
      env:
        NPM_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
    - run: yarn install
    - run: REACT_APP_TG_API_ID=${APP_ID} REACT_APP_TG_API_HASH=${APP_HASH} GENERATE_SOURCEMAP=false yarn workspaces run build
      env:
        APP_ID: ${{ secrets.TG_API_ID }}
        APP_HASH: ${{ secrets.TG_API_HASH }}
    - name: Compress node_modules root
      if: ${{ github.event_name == 'push' }}
      run: tar -czf build-root.tar.gz node_modules
    - name: Archive build-root.tar.gz
      uses: actions/upload-artifact@v2
      with:
        name: build-root
        path: build-root.tar.gz
    - name: Compress node_modules & dist server
      if: ${{ github.event_name == 'push' }}
      run: tar -czf build-server.tar.gz server/node_modules server/dist
    - name: Archive build-server.tar.gz
      uses: actions/upload-artifact@v2
      with:
        name: build-server
        path: build-server.tar.gz
    - name: Compress node_modules & build web
      if: ${{ github.event_name == 'push' }}
      run: tar -czf build-web.tar.gz web/node_modules web/build
    - name: Archive build-web.tar.gz
      uses: actions/upload-artifact@v2
      with:
        name: build-web
        path: build-web.tar.gz

  sg1:
    name: Upload & Deploy to SG1
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Download all workflow run artifacts
        uses: actions/download-artifact@v2

      - run: mv build-root/build-root.tar.gz . && mv build-server/build-server.tar.gz . && mv build-web/build-web.tar.gz .

      # Upload to SG_1
      - name: Upload build-root.tar.gz to sg_1
        uses: appleboy/scp-action@master
        with:
          source: build-root.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_SG_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          debug: true
      - name: Upload build-server.tar.gz to sg_1
        uses: appleboy/scp-action@master
        with:
          source: build-server.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_SG_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Upload build-web.tar.gz to sg_1
        uses: appleboy/scp-action@master
        with:
          source: build-web.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_SG_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}

      # Deployment SG_1
      - name: Extract build-root.tar.gz to sg_1
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }} && rm -rf node_modules && tar -xzf build-root.tar.gz && rm -rf build-root.tar.gz
          host: ${{ env.DEV_HOST_SG_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-web.tar.gz to sg_1
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/web && rm -rf node_modules && rm -rf build && cd .. && tar -xzf build-web.tar.gz && rm -rf build-web.tar.gz
          host: ${{ env.DEV_HOST_SG_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-server.tar.gz to sg_1
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/server && rm -rf node_modules && rm -rf dist && cd .. && tar -xzf build-server.tar.gz && rm -rf build-server.tar.gz && git pull origin main --rebase && cd ${{ env.APP_DIR }} && /usr/local/bin/pm2 restart td
          host: ${{ env.DEV_HOST_SG_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}

      # Notification
      - name: Success Notification
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🎉 Deployed to SG_1
          format: markdown
      - name: Failed Notification
        if: ${{ failure() }}
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🔥 Deploy to SG_1 Failed!
          format: markdown

  sg2:
    name: Upload & Deploy to SG2
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Download all workflow run artifacts
        uses: actions/download-artifact@v2

      - run: mv build-root/build-root.tar.gz . && mv build-server/build-server.tar.gz . && mv build-web/build-web.tar.gz .

      # Upload to SG_2
      - name: Upload build-root.tar.gz to sg_2
        uses: appleboy/scp-action@master
        with:
          source: build-root.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_SG_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_SG_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
          debug: true
      - name: Upload build-server.tar.gz to sg_2
        uses: appleboy/scp-action@master
        with:
          source: build-server.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_SG_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_SG_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
      - name: Upload build-web.tar.gz to sg_2
        uses: appleboy/scp-action@master
        with:
          source: build-web.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_SG_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_SG_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}

      # Deployment SG_2
      - name: Extract build-root.tar.gz to sg_2
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }} && rm -rf node_modules && tar -xzf build-root.tar.gz && rm -rf build-root.tar.gz
          host: ${{ env.DEV_HOST_SG_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_SG_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-web.tar.gz to sg_2
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/web && rm -rf node_modules && rm -rf build && cd .. && tar -xzf build-web.tar.gz && rm -rf build-web.tar.gz
          host: ${{ env.DEV_HOST_SG_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_SG_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-server.tar.gz to sg_2
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/server && rm -rf node_modules && rm -rf dist && cd .. && tar -xzf build-server.tar.gz && rm -rf build-server.tar.gz && git pull origin main --rebase && cd ${{ env.APP_DIR }} && /usr/local/bin/pm2 restart td
          host: ${{ env.DEV_HOST_SG_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_SG_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}

      # Notification
      - name: Success Notification
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🎉 Deployed to SG_2
          format: markdown
      - name: Failed Notification
        if: ${{ failure() }}
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🔥 Deploy to SG_2 Failed!
          format: markdown

  ge1:
    name: Upload & Deploy to GE1
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Download all workflow run artifacts
        uses: actions/download-artifact@v2

      - run: mv build-root/build-root.tar.gz . && mv build-server/build-server.tar.gz . && mv build-web/build-web.tar.gz .

      # Upload to GE_1
      - name: Upload build-root.tar.gz to ge_1
        uses: appleboy/scp-action@master
        with:
          source: build-root.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_GE_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          debug: true
      - name: Upload build-server.tar.gz to ge_1
        uses: appleboy/scp-action@master
        with:
          source: build-server.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_GE_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Upload build-web.tar.gz to ge_1
        uses: appleboy/scp-action@master
        with:
          source: build-web.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_GE_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}

      # Deployment GE_1
      - name: Extract build-root.tar.gz to ge_1
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }} && rm -rf node_modules && tar -xzf build-root.tar.gz && rm -rf build-root.tar.gz
          host: ${{ env.DEV_HOST_GE_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-web.tar.gz to ge_1
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/web && rm -rf node_modules && rm -rf build && cd .. && tar -xzf build-web.tar.gz && rm -rf build-web.tar.gz
          host: ${{ env.DEV_HOST_GE_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-server.tar.gz to ge_1
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/server && rm -rf node_modules && rm -rf dist && cd .. && tar -xzf build-server.tar.gz && rm -rf build-server.tar.gz && git pull origin main --rebase && cd ${{ env.APP_DIR }} && /usr/local/bin/pm2 restart td
          host: ${{ env.DEV_HOST_GE_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}

      # Notification
      - name: Success Notification
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🎉 Deployed to GE_1
          format: markdown
      - name: Failed Notification
        if: ${{ failure() }}
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🔥 Deploy to GE_1 Failed!
          format: markdown

  ge2:
    name: Upload & Deploy to GE2
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Download all workflow run artifacts
        uses: actions/download-artifact@v2

      - run: mv build-root/build-root.tar.gz . && mv build-server/build-server.tar.gz . && mv build-web/build-web.tar.gz .

      # Upload to GE_2
      - name: Upload build-root.tar.gz to ge_2
        uses: appleboy/scp-action@master
        with:
          source: build-root.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_GE_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_GE_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
          debug: true
      - name: Upload build-server.tar.gz to ge_2
        uses: appleboy/scp-action@master
        with:
          source: build-server.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_GE_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_GE_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
      - name: Upload build-web.tar.gz to ge_2
        uses: appleboy/scp-action@master
        with:
          source: build-web.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_GE_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_GE_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}

      # Deployment GE_2
      - name: Extract build-root.tar.gz to ge_2
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }} && rm -rf node_modules && tar -xzf build-root.tar.gz && rm -rf build-root.tar.gz
          host: ${{ env.DEV_HOST_GE_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_GE_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-web.tar.gz to ge_2
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/web && rm -rf node_modules && rm -rf build && cd .. && tar -xzf build-web.tar.gz && rm -rf build-web.tar.gz
          host: ${{ env.DEV_HOST_GE_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_GE_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-server.tar.gz to ge_2
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/server && rm -rf node_modules && rm -rf dist && cd .. && tar -xzf build-server.tar.gz && rm -rf build-server.tar.gz && git pull origin main --rebase && cd ${{ env.APP_DIR }} && /usr/local/bin/pm2 restart td
          host: ${{ env.DEV_HOST_GE_2 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          proxy_host: ${{ env.DEV_HOST_GE_1 }}
          proxy_username: ${{ env.DEV_USER }}
          proxy_key: ${{ secrets.SSH_SECRET }}

      # Notification
      - name: Success Notification
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🎉 Deployed to GE_2
          format: markdown
      - name: Failed Notification
        if: ${{ failure() }}
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🔥 Deploy to GE_2 Failed!
          format: markdown

  us1:
    name: Upload & Deploy to US1
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Download all workflow run artifacts
        uses: actions/download-artifact@v2

      - run: mv build-root/build-root.tar.gz . && mv build-server/build-server.tar.gz . && mv build-web/build-web.tar.gz .

      # Upload to US_1
      - name: Upload build-root.tar.gz to us_1
        uses: appleboy/scp-action@master
        with:
          source: build-root.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_US_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
          debug: true
      - name: Upload build-server.tar.gz to us_1
        uses: appleboy/scp-action@master
        with:
          source: build-server.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_US_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Upload build-web.tar.gz to us_1
        uses: appleboy/scp-action@master
        with:
          source: build-web.tar.gz
          target: ${{ env.APP_DIR }}
          host: ${{ env.DEV_HOST_US_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}

      # Deployment US_1
      - name: Extract build-root.tar.gz to us_1
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }} && rm -rf node_modules && tar -xzf build-root.tar.gz && rm -rf build-root.tar.gz
          host: ${{ env.DEV_HOST_US_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-web.tar.gz to us_1
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/web && rm -rf node_modules && rm -rf build && cd .. && tar -xzf build-web.tar.gz && rm -rf build-web.tar.gz
          host: ${{ env.DEV_HOST_US_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}
      - name: Extract build-server.tar.gz to us_1
        if: ${{ github.event_name == 'push' }}
        uses: appleboy/ssh-action@master
        with:
          script: cd ${{ env.APP_DIR }}/server && rm -rf node_modules && rm -rf dist && cd .. && tar -xzf build-server.tar.gz && rm -rf build-server.tar.gz && git pull origin main --rebase && cd ${{ env.APP_DIR }} && /usr/local/bin/pm2 restart td
          host: ${{ env.DEV_HOST_US_1 }}
          username: ${{ env.DEV_USER }}
          key: ${{ secrets.SSH_SECRET }}

      # Notification
      - name: Success Notification
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🎉 Deployed to US_1
          format: markdown
      - name: Failed Notification
        if: ${{ failure() }}
        uses: appleboy/telegram-action@master
        with:
          to: -700697945
          token: ${{ secrets.TG_BOT_TOKEN }}
          message: 🔥 Deploy to US_1 Failed!
          format: markdown
