on:
  push:
    branches:
      - staging

name: Build Staging

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v1
      with:
        node-version: '14.x'
    - name: Add npmrc
      run: echo -e "//npm.pkg.github.com/:_authToken=${NPM_AUTH_TOKEN}\n@mgilangjanuar:registry=https://npm.pkg.github.com/" > ~/.npmrc && cat ~/.npmrc
      env:
        NPM_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
    - run: yarn install
    - run: REACT_APP_TG_API_ID=${APP_ID} REACT_APP_TG_API_HASH=${APP_HASH} yarn workspaces run build
      env:
        APP_ID: ${{ secrets.TG_API_ID }}
        APP_HASH: ${{ secrets.TG_API_HASH }}
    - run: npm i vercel -g
    - name: "Deploy to Vercel"
      run: npx vercel --token ${VERCEL_TOKEN} --prod
      env:
        VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
        VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    - name: Success Notification
      uses: appleboy/telegram-action@master
      with:
        to: -700697945
        token: ${{ secrets.TG_BOT_TOKEN }}
        message: |
          🎉 *Deployed to Staging*

          Please take a look -> https://teledrive.vercel.app
        format: markdown
    - name: Failed Notification
      if: ${{ failure() }}
      uses: appleboy/telegram-action@master
      with:
        to: -700697945
        token: ${{ secrets.TG_BOT_TOKEN }}
        message: |
          🔥 *Deploy to Staging Failed!*

          Please take a look -> https://github.com/mgilangjanuar/teledrive/actions
        format: markdown