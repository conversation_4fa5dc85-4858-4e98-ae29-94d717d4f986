{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "2.0.0-beta.18", "@docusaurus/preset-classic": "2.0.0-beta.18", "@mdx-js/react": "^1.6.22", "clsx": "^1.1.1", "prism-react-renderer": "^1.3.1", "react": "^17.0.2", "react-dom": "^17.0.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}