version: '3'

services:
  teledrive:
    command: bash -c "export NODE_OPTIONS='--openssl-legacy-provider --no-experimental-fetch' && yarn api prisma migrate deploy || yarn api prisma migrate resolve --applied "20220420012853_init" && node api/dist/index.js"
    labels:
      traefik.http.routers.server.rule: Host(`teledrive.localhost`)
      traefik.port: 4000
    ports:
      - "${PORT:-4000}:${PORT:-4000}"
    expose:
      - ${PORT:-4000}
    build:
      context: ../.
      dockerfile: Dockerfile
      args:
        REACT_APP_TG_API_ID: ${TG_API_ID}
        REACT_APP_TG_API_HASH: ${TG_API_HASH}
    environment:
      DATABASE_URL: postgres://postgres:${DB_PASSWORD}@db:5432/teledrive?connect_timeout=60&pool_timeout=60&socket_timeout=60
      REDIS_URI: redis://redis:6379
    env_file:
      - .env
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: teledrive
    volumes:
      - ./data:/var/lib/postgresql/data

  redis:
    image: redis:6
    restart: always

volumes:
  teledrive_data:
