{"version": 2, "env": {}, "builds": [{"src": "api/dist/index.js", "use": "@vercel/node", "config": {"includeFiles": ["api/dist/**"], "excludeFiles": ["web/**", "android/**", "docs/**", "docker/**", "press/**", "electron/**", "integrations/**"]}}, {"src": "web/build/**", "use": "@vercel/static", "config": {"includeFiles": ["web/build/**"], "excludeFiles": ["api/**", "android/**", "docs/**", "docker/**", "press/**", "electron/**", "integrations/**"]}}], "routes": [{"src": "/(.*)", "dest": "api/dist/index.js"}, {"src": "/", "dest": "web/build/index.html"}, {"src": "/(.+)", "dest": "web/build/$1"}]}