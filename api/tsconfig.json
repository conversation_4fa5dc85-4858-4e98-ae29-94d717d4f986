{"compilerOptions": {"module": "commonjs", "target": "es6", "lib": ["es5", "es6", "es2017", "dom"], "types": ["reflect-metadata", "node"], "noImplicitAny": false, "moduleResolution": "node", "esModuleInterop": true, "removeComments": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "resolveJsonModule": true, "skipLibCheck": true}, "exclude": ["tests", "node_modules", "dist"]}