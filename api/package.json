{"name": "api", "version": "2.5.5", "main": "dist/index.js", "license": "MIT", "private": true, "scripts": {"start": "nodemon dist/index.js --watch", "prebuild": "prisma generate", "build": "rimraf dist && eslint -c .eslintrc.js --ext .ts . && tsc", "postbuild": "test -f keys || (node -e \"console.log(require('crypto').randomBytes(48).toString('base64'));\" > keys && node -e \"console.log(require('crypto').randomBytes(48).toString('base64'));\" >> keys)"}, "dependencies": {"@prisma/client": "^4.12.0", "@types/moment": "^2.13.0", "axios": "^0.21.4", "bcryptjs": "^2.4.3", "big-integer": "^1.6.48", "check-disk-space": "^3.3.0", "compression": "^1.7.4", "content-disposition": "^0.5.3", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "curly-express": "^1.3.6", "dotenv": "^10.0.0", "express": "^4.17.1", "express-ipinfo": "^1.0.0", "express-list-endpoints": "^6.0.0", "express-rate-limit": "^5.3.0", "flatted": "^3.2.2", "geoip-lite": "^1.4.2", "human-format": "^1.0.0", "input": "^1.0.1", "ioredis": "^4.28.2", "is-uuid": "^1.0.2", "json-bigint": "^1.0.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.1", "morgan": "^1.10.0", "multer": "^1.4.3", "nanoid": "^3.1.25", "pg": "^8.7.1", "pg-connection-string": "^2.5.0", "prisma": "^4.12.0", "redis": "^4.0.1", "serialize-error": "^8.1.0", "serverless-http": "^3.0.1", "source-map-support": "^0.5.19", "telegram": "^2.15.5", "uuid-random": "^1.3.2", "util": "0.12.5", "path": "0.12.7"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/bcryptjs": "^2.4.2", "@types/big-integer": "^0.0.31", "@types/compression": "^1.7.2", "@types/content-disposition": "^0.5.4", "@types/cookie-parser": "^1.4.2", "@types/cors": "^2.8.12", "@types/crypto-js": "^4.0.2", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.13", "@types/express-list-endpoints": "^6.0.0", "@types/express-rate-limit": "^5.1.3", "@types/geoip-lite": "^1.4.1", "@types/ioredis": "^4.28.7", "@types/is-uuid": "^1.0.0", "@types/jsonwebtoken": "^8.5.5", "@types/morgan": "^1.9.3", "@types/multer": "^1.4.7", "@types/nanoid": "^3.0.0", "@types/node": "^16.7.2", "@types/pg": "^8.6.1", "@types/pg-connection-string": "^2.0.0", "@types/redis": "^4.0.11", "@types/serialize-error": "^4.0.1", "@types/source-map-support": "^0.5.4", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "eslint": "^7.32.0", "nodemon": "^2.0.12", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "typescript": "^4.4.2"}}