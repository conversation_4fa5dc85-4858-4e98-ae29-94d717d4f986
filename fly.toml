# fly.toml app configuration file generated for tldrive on 2023-05-24T11:46:09+07:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "tldrive"
primary_region = "sin"
kill_signal = "SIGINT"
kill_timeout = "5s"

[[services]]
  protocol = "tcp"
  internal_port = 4000
  processes = ["app"]
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]
