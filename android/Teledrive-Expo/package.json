{"main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"expo": "~44.0.0", "expo-cli": "^5.4.6", "expo-constants": "~13.0.2", "expo-status-bar": "~1.2.0", "expo-updates": "~0.11.7", "react": "17.0.1", "react-dom": "17.0.1", "react-native": "0.64.3", "react-native-web": "0.17.1", "react-native-webview": "11.15.0"}, "devDependencies": {"@babel/core": "^7.12.9"}, "private": true}