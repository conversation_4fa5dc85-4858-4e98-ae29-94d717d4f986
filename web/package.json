{"name": "web", "version": "2.5.4", "private": true, "dependencies": {"@ideasio/add-to-homescreen-react": "^1.0.10", "antd": "4.18.3", "antd-country-phone-input": "^4.3.1", "axios": "^1.3.5", "base64url": "^3.0.1", "clipboardy": "^2.3.0", "js-cookie": "^3.0.1", "less": "^4.1.2", "lessc": "^1.0.2", "mediasource": "^2.4.0", "millify": "^4.0.0", "mime-types": "^2.1.32", "pretty-bytes": "^5.6.0", "pump": "^3.0.0", "pwa-install-handler": "^2.1.6", "qs": "^6.10.1", "react": "^17.0.2", "react-chat-elements": "^10.16.0", "react-css-theme-switcher": "^0.3.0", "react-dnd": "^14.0.4", "react-dnd-html5-backend": "^15.1.2", "react-doc-viewer": "^0.1.5", "react-dom": "^17.0.2", "react-github-btn": "^1.2.1", "react-helmet": "^6.1.0", "react-loading-screen": "^0.0.17", "react-markdown": "^7.0.1", "react-otp-input": "^2.4.0", "react-paypal-button-v2": "^2.6.3", "react-player": "^2.10.1", "react-qr-code": "^2.0.7", "react-router-dom": "^5.3.0", "react-scripts": "^4.0.3", "react-twitter-widgets": "^1.10.0", "react-virtualized": "^9.22.3", "remark-gfm": "^2.0.0", "stream-to-blob": "^2.0.1", "stream-to-blob-url": "^3.0.2", "streamsaver": "^2.0.5", "sw-precache-cra": "^1.0.0", "swr": "^1.0.0", "telegram": "^2.15.5", "through2": "^4.0.2", "typescript": "^4.0.3", "use-debounce": "^7.0.1", "web-vitals": "^0.2.4", "world_countries_lists": "^2.5.1", "concat-stream": "^2.0.0", "axios-parallel": "^1.1.2", "fastpriorityqueue": "0.7.4"}, "scripts": {"start": "react-scripts --openssl-legacy-provider --no-experimental-fetch start", "build": "react-scripts --openssl-legacy-provider --no-experimental-fetch build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/clipboardy": "^2.0.1", "@types/jest": "^26.0.15", "@types/js-cookie": "^2.2.7", "@types/less": "^3.0.3", "@types/mime-types": "^2.1.1", "@types/node": "^12.0.0", "@types/qs": "^6.9.7", "@types/react": "^16.9.53", "@types/react-dom": "^16.9.8", "@types/react-helmet": "^6.1.5", "@types/react-otp-input": "^2.0.1", "@types/react-router-dom": "^5.1.8", "@types/react-virtualized": "^9.21.13", "@types/streamsaver": "^2.0.1", "@typescript-eslint/eslint-plugin": "^4.31.0", "@typescript-eslint/parser": "^4.31.0", "eslint": "^7.32.0", "workbox-background-sync": "^5.1.3", "workbox-broadcast-update": "^5.1.3", "workbox-cacheable-response": "^5.1.3", "workbox-core": "^5.1.3", "workbox-expiration": "^5.1.3", "workbox-google-analytics": "^5.1.3", "workbox-navigation-preload": "^5.1.3", "workbox-precaching": "^5.1.3", "workbox-range-requests": "^5.1.3", "workbox-routing": "^5.1.3", "workbox-strategies": "^5.1.3", "workbox-streams": "^5.1.3"}}