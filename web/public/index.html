<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico?v=2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- <meta name="theme-color" content="#0088CC" /> -->
    <meta name="description" content="The Google Drive/OneDrive/etc alternative using Telegram API" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="TeleDrive - The Free Unlimited Cloud Storage" />
    <meta property="og:description" content="The Google Drive/OneDrive/etc alternative using Telegram API" />
    <meta property="og:image" content="https://drive.google.com/uc?id=1o2HnKglEF0-cvtNmQqWZicJnSCSmnoEr" />
    <meta property="og:url" content="https://teledrive.vercel.app" />
    <meta name="twitter:title" content="TeleDrive - The Free Unlimited Cloud Storage" />
    <meta name="twitter:description" content=" The Google Drive/OneDrive/etc alternative using Telegram API" />
    <meta name="twitter:image" content="https://drive.google.com/uc?id=1o2HnKglEF0-cvtNmQqWZicJnSCSmnoEr" />
    <meta name="twitter:card" content="summary_large_image" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png?v=2" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>TeleDrive - The Free Unlimited Cloud Storage</title>
    <!-- <script async defer data-website-id="9cbb2118-c7eb-46e2-9b16-adf99945194c" src="https://analytics.teledriveapp.com/umami.js"></script> -->
    <style>
      @font-face { font-family: 'Plus Jakarta Display Medium'; src: url('PlusJakartaDisplay-Medium.woff'); }
      @font-face { font-family: 'Plus Jakarta Display Medium'; src: url('PlusJakartaDisplay-Medium.woff2'); }
      @font-face { font-family: 'PlusJakartaSans-Bold'; src: url('PlusJakartaSans-Bold.woff'); }
      @font-face { font-family: 'PlusJakartaSans-Bold'; src: url('PlusJakartaSans-Bold.woff2'); }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <!-- <script data-name="BMC-Widget" data-cfasync="false" src="https://cdnjs.buymeacoffee.com/1.0.0/widget.prod.min.js" data-id="mgilangjanuar" data-description="Support me on Buy me a coffee!" data-message="" data-color="#FFDD00" data-position="Right" data-x_margin="18" data-y_margin="18"></script> -->
    <!-- <script src="https://www.paypal.com/sdk/js?client-id=ARkJh0Em9yWYBKikZg-hoGUsuiRBEuJ81Aupf97NKqqLhSzVmDfhUdr48Q5ZoNVRrXLXQBbmUoWh94PL"></script> -->
  </body>
</html>
